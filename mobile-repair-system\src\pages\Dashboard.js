import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { 
  <PERSON>a<PERSON><PERSON><PERSON>, 
  FaList, 
  FaCogs, 
  FaMoneyBillWave, 
  FaChartLine, 
  FaCalendarAlt,
  FaExclamationTriangle,
  FaCheckCircle,
  FaClock,
  FaTools
} from 'react-icons/fa';

import Sidebar from '../components/Sidebar';
import SearchBar from '../components/SearchBar';

const DashboardContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: ${props => props.theme.colors.background};
`;

const MainContent = styled.div`
  flex: 1;
  margin-right: 280px;
  padding: ${props => props.theme.spacing.xl};
  transition: margin-right 0.3s ease;

  @media (max-width: 768px) {
    margin-right: 0;
    padding: ${props => props.theme.spacing.lg};
  }
`;

const Header = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const HeaderTitle = styled.h1`
  font-size: ${props => props.theme.fonts.sizes.xxlarge};
  font-weight: 700;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.md};
  text-align: center;
`;

const HeaderSubtitle = styled.p`
  font-size: ${props => props.theme.fonts.sizes.medium};
  color: ${props => props.theme.colors.textSecondary};
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const SearchContainer = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const StatCard = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 45px rgba(31, 38, 135, 0.5);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: ${props => props.color || props.theme.colors.primary};
  }
`;

const StatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const StatIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: ${props => props.theme.shadows.medium};
`;

const StatValue = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.colors.textPrimary};
  text-align: left;
`;

const StatTitle = styled.h3`
  font-size: ${props => props.theme.fonts.sizes.large};
  font-weight: 600;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const StatDescription = styled.p`
  font-size: ${props => props.theme.fonts.sizes.small};
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.5;
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const QuickActionCard = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(31, 38, 135, 0.6);
  }

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
  }

  &:hover::before {
    opacity: 1;
    animation: shimmer 0.6s ease-in-out;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  }
`;

const ActionIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.colors.background};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  margin: 0 auto ${props => props.theme.spacing.lg};
  box-shadow: ${props => props.theme.shadows.medium};
  position: relative;
  z-index: 1;
`;

const ActionTitle = styled.h3`
  font-size: ${props => props.theme.fonts.sizes.large};
  font-weight: 600;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.sm};
  position: relative;
  z-index: 1;
`;

const ActionDescription = styled.p`
  font-size: ${props => props.theme.fonts.sizes.medium};
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.5;
  position: relative;
  z-index: 1;
`;

const RecentActivity = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fonts.sizes.xlarge};
  font-weight: 600;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

const ActivityItem = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: rgba(102, 126, 234, 0.05);
    border-radius: ${props => props.theme.borderRadius.small};
  }
`;

const ActivityIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  margin-left: ${props => props.theme.spacing.md};
`;

const ActivityContent = styled.div`
  flex: 1;
`;

const ActivityTitle = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const ActivityTime = styled.div`
  font-size: ${props => props.theme.fonts.sizes.small};
  color: ${props => props.theme.colors.textSecondary};
`;

function Dashboard({ onLogout }) {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalCustomers: 0,
    totalOrders: 0,
    totalSpareParts: 0,
    totalDebts: 0,
    pendingOrders: 0,
    completedOrders: 0,
    inProgressOrders: 0,
    failedOrders: 0
  });

  const [recentActivities] = useState([
    {
      id: 1,
      type: 'order',
      title: 'طلب صيانة جديد من أحمد محمد',
      time: 'منذ 5 دقائق',
      icon: FaTools,
      color: '#28a745'
    },
    {
      id: 2,
      type: 'payment',
      title: 'تم تسديد دين بقيمة 150,000 دينار',
      time: 'منذ 15 دقيقة',
      icon: FaMoneyBillWave,
      color: '#17a2b8'
    },
    {
      id: 3,
      type: 'customer',
      title: 'تم إضافة عميل جديد: فاطمة حسن',
      time: 'منذ 30 دقيقة',
      icon: FaUsers,
      color: '#667eea'
    },
    {
      id: 4,
      type: 'order_complete',
      title: 'تم إكمال صيانة الهاتف رقم 20241220001',
      time: 'منذ ساعة',
      icon: FaCheckCircle,
      color: '#28a745'
    }
  ]);

  useEffect(() => {
    // محاكاة جلب الإحصائيات من قاعدة البيانات
    const fetchStats = async () => {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        totalCustomers: 156,
        totalOrders: 342,
        totalSpareParts: 89,
        totalDebts: 2450000,
        pendingOrders: 23,
        completedOrders: 298,
        inProgressOrders: 15,
        failedOrders: 6
      });
    };

    fetchStats();
  }, []);

  const handleSearchResult = (result) => {
    switch (result.type) {
      case 'customer':
        navigate('/customers', { state: { selectedCustomer: result } });
        break;
      case 'order':
        navigate('/orders', { state: { selectedOrder: result } });
        break;
      case 'spare_part':
        navigate('/spare-parts', { state: { selectedPart: result } });
        break;
      default:
        break;
    }
  };

  const quickActions = [
    {
      title: 'إضافة صيانة',
      description: 'إضافة طلب صيانة جديد',
      icon: FaTools,
      color: '#28a745',
      path: '/add-order'
    },
    {
      title: 'عرض الطلبات',
      description: 'عرض وإدارة طلبات الصيانة',
      icon: FaList,
      color: '#17a2b8',
      path: '/orders'
    },
    {
      title: 'إدارة العملاء',
      description: 'عرض وإدارة بيانات العملاء',
      icon: FaUsers,
      color: '#667eea',
      path: '/customers'
    },
    {
      title: 'قطع الغيار',
      description: 'إدارة مخزون قطع الغيار',
      icon: FaCogs,
      color: '#ffc107',
      path: '/spare-parts'
    }
  ];

  return (
    <DashboardContainer>
      <Sidebar onLogout={onLogout} />
      <MainContent>
        <Header>
          <HeaderTitle>مرحباً بك في نظام إدارة صيانة الموبايلات</HeaderTitle>
          <HeaderSubtitle>
            إدارة شاملة لجميع عمليات الصيانة والعملاء والمخزون
          </HeaderSubtitle>
          <SearchContainer>
            <SearchBar onResultClick={handleSearchResult} />
          </SearchContainer>
        </Header>

        <StatsGrid>
          <StatCard color="#667eea">
            <StatHeader>
              <StatIcon color="#667eea">
                <FaUsers />
              </StatIcon>
              <StatValue>{stats.totalCustomers.toLocaleString()}</StatValue>
            </StatHeader>
            <StatTitle>إجمالي العملاء</StatTitle>
            <StatDescription>
              عدد العملاء المسجلين في النظام
            </StatDescription>
          </StatCard>

          <StatCard color="#28a745">
            <StatHeader>
              <StatIcon color="#28a745">
                <FaList />
              </StatIcon>
              <StatValue>{stats.totalOrders.toLocaleString()}</StatValue>
            </StatHeader>
            <StatTitle>إجمالي الطلبات</StatTitle>
            <StatDescription>
              عدد طلبات الصيانة الكلية
            </StatDescription>
          </StatCard>

          <StatCard color="#ffc107">
            <StatHeader>
              <StatIcon color="#ffc107">
                <FaCogs />
              </StatIcon>
              <StatValue>{stats.totalSpareParts.toLocaleString()}</StatValue>
            </StatHeader>
            <StatTitle>قطع الغيار</StatTitle>
            <StatDescription>
              عدد قطع الغيار المتوفرة
            </StatDescription>
          </StatCard>

          <StatCard color="#dc3545">
            <StatHeader>
              <StatIcon color="#dc3545">
                <FaMoneyBillWave />
              </StatIcon>
              <StatValue>{(stats.totalDebts / 1000).toFixed(0)}K</StatValue>
            </StatHeader>
            <StatTitle>إجمالي الديون</StatTitle>
            <StatDescription>
              {stats.totalDebts.toLocaleString()} دينار عراقي
            </StatDescription>
          </StatCard>
        </StatsGrid>

        <QuickActionsGrid>
          {quickActions.map((action, index) => (
            <QuickActionCard 
              key={index} 
              onClick={() => navigate(action.path)}
            >
              <ActionIcon color={action.color}>
                <action.icon />
              </ActionIcon>
              <ActionTitle>{action.title}</ActionTitle>
              <ActionDescription>{action.description}</ActionDescription>
            </QuickActionCard>
          ))}
        </QuickActionsGrid>

        <RecentActivity>
          <SectionTitle>
            <FaChartLine />
            النشاط الأخير
          </SectionTitle>
          {recentActivities.map((activity) => (
            <ActivityItem key={activity.id}>
              <ActivityIcon color={activity.color}>
                <activity.icon />
              </ActivityIcon>
              <ActivityContent>
                <ActivityTitle>{activity.title}</ActivityTitle>
                <ActivityTime>{activity.time}</ActivityTime>
              </ActivityContent>
            </ActivityItem>
          ))}
        </RecentActivity>
      </MainContent>
    </DashboardContainer>
  );
}

export default Dashboard;
