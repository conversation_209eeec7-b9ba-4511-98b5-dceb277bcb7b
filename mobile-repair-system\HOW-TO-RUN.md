# 🚀 How to Run Mobile Repair Management System

## 📋 Quick Start Guide

### **Method 1: Easiest Way (Recommended)**

1. **Double-click `check-system.bat`**
   - This will check all requirements and show any problems

2. **Double-click `install-system.bat`**
   - This will install all required libraries automatically
   - Wait until installation completes

3. **Double-click `quick-start.bat`**
   - This will start the system and open it in your browser
   - System will open at: http://localhost:3000

4. **Use login credentials:**
   - Username: `abd`
   - Password: `ZAin1998`

---

### **Method 2: Manual Installation**

1. **Open Command Prompt**
2. **Navigate to system folder:**
   ```cmd
   cd "c:\Users\<USER>\Desktop\برنامج الصيانة 206\mobile-repair-system"
   ```

3. **Install libraries:**
   ```cmd
   npm install
   ```

4. **Start system:**
   ```cmd
   npm start
   ```

5. **Open browser and go to:**
   ```
   http://localhost:3000
   ```

---

### **Method 3: Quick Demo (No Installation)**

- **Double-click `demo.html`**
- This will show a demo of the system without installation

---

## 🔧 System Requirements

### **Required:**
- **Node.js** (Download from: https://nodejs.org)
- **Windows 10** or newer
- **4 GB RAM** minimum
- **500 MB** free disk space
- **Internet connection** (for initial setup)

---

## 📁 Important Files

- `check-system.bat` - Check system requirements
- `install-system.bat` - Install all libraries
- `quick-start.bat` - Start system quickly
- `start.bat` - Main menu with options
- `demo.html` - Demo without installation

---

## 🚨 Troubleshooting

### **Problem: "node is not recognized"**
**Solution:** Install Node.js from https://nodejs.org

### **Problem: Installation fails**
**Solution:** 
1. Run Command Prompt as Administrator
2. Clear npm cache: `npm cache clean --force`
3. Try again: `npm install`

### **Problem: System doesn't open**
**Solution:**
1. Wait for "compiled successfully" message
2. Open browser manually
3. Go to: http://localhost:3000

### **Problem: Files open and close immediately**
**Solution:**
1. Right-click on .bat file
2. Select "Run as administrator"
3. Or use Command Prompt method

---

## 🔑 Login Credentials

```
Username: abd
Password: ZAin1998
```

---

## ✅ System Features

### **Completed Features:**
- 🔐 Secure login system
- 🏠 Dashboard with live statistics
- 🔍 Smart search system
- 👥 Customer management
- 🔧 Repair order creation
- 🎨 Beautiful 3D design
- 🌐 Full Arabic language support
- 💰 Iraqi Dinar currency

### **In Development:**
- 📋 Order management
- 🔩 Spare parts management
- 📄 Document generation
- 📊 Reports and analytics
- 📝 Activity log
- ⚙️ Settings panel

---

## 🎯 Success Indicators

When system runs successfully, you will see:
- "compiled successfully" message in terminal
- Browser opens automatically to localhost:3000
- Beautiful login page appears
- Arabic fonts display correctly

---

## 📞 Support

If you encounter problems:
1. Check this guide
2. Run `check-system.bat`
3. Try `demo.html` for quick preview
4. Ensure Node.js is properly installed

---

**System created successfully ✅ and ready to use! 🚀**

**Developed with ❤️ for Iraqi Market**
