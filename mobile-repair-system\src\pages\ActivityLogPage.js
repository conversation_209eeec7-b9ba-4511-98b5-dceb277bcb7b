import React from 'react';
import styled from 'styled-components';
import Sidebar from '../components/Sidebar';

const PageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: ${props => props.theme.colors.background};
`;

const MainContent = styled.div`
  flex: 1;
  margin-right: 280px;
  padding: ${props => props.theme.spacing.xl};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ComingSoon = styled.div`
  text-align: center;
  color: white;
  font-size: 24px;
`;

function ActivityLogPage({ onLogout }) {
  return (
    <PageContainer>
      <Sidebar onLogout={onLogout} />
      <MainContent>
        <ComingSoon>صفحة السجل - قيد التطوير</ComingSoon>
      </MainContent>
    </PageContainer>
  );
}

export default ActivityLogPage;
