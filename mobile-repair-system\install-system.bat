@echo off
title Mobile Repair System - Installation
color 0B

echo.
echo ========================================
echo   Mobile Repair System - Installation
echo ========================================
echo.

echo Checking system requirements...
echo.

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed on the system
    echo Please download and install Node.js from: https://nodejs.org
    echo.
    pause
    exit
) else (
    echo OK: Node.js is installed
)

:: Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not available
    pause
    exit
) else (
    echo OK: npm is available
)

echo.
echo Installing basic libraries...
echo.

:: Install basic libraries
call npm install react react-dom react-router-dom styled-components react-icons

echo.
echo Installing React Scripts...
echo.

call npm install react-scripts

echo.
echo Installing Electron...
echo.

call npm install electron --save-dev

echo.
echo Installing additional libraries...
echo.

call npm install better-sqlite3 jspdf html2canvas exceljs qrcode date-fns react-select react-datepicker

echo.
echo ========================================
echo        Installation Completed!
echo ========================================
echo.

echo To run the system:
echo 1. Double-click on quick-start.bat
echo 2. Or type in Terminal: npm start
echo.

echo Login Credentials:
echo Username: abd
echo Password: ZAin1998
echo.

echo Creating desktop shortcut...

:: Create desktop shortcut
set "desktop=%USERPROFILE%\Desktop"
set "shortcut=%desktop%\Mobile Repair System.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut%'); $Shortcut.TargetPath = '%CD%\quick-start.bat'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'Mobile Repair Management System'; $Shortcut.Save()"

echo.
echo OK: Desktop shortcut created
echo.

pause
