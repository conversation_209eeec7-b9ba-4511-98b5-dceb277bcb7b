import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  <PERSON>a<PERSON>ser, 
  FaPhone, 
  FaMapMarkerAlt, 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaMoneyBillWave,
  FaEye,
  FaSearch
} from 'react-icons/fa';

import Sidebar from '../components/Sidebar';

const PageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: ${props => props.theme.colors.background};
`;

const MainContent = styled.div`
  flex: 1;
  margin-right: 280px;
  padding: ${props => props.theme.spacing.xl};
  transition: margin-right 0.3s ease;

  @media (max-width: 768px) {
    margin-right: 0;
    padding: ${props => props.theme.spacing.lg};
  }
`;

const Header = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};
`;

const HeaderTitle = styled.h1`
  font-size: ${props => props.theme.fonts.sizes.xxlarge};
  font-weight: 700;
  color: ${props => props.theme.colors.textPrimary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  align-items: center;
`;

const SearchInput = styled.input`
  padding: ${props => props.theme.spacing.md};
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: ${props => props.theme.borderRadius.medium};
  background: rgba(255, 255, 255, 0.9);
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  transition: all 0.3s ease;
  text-align: right;
  min-width: 250px;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 1);
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
    text-align: right;
  }
`;

const AddButton = styled.button`
  background: linear-gradient(145deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  box-shadow: ${props => props.theme.shadows.medium};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.large};
  }
`;

const CustomersGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: ${props => props.theme.spacing.lg};
`;

const CustomerCard = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 45px rgba(31, 38, 135, 0.5);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: ${props => props.theme.colors.primary};
  }
`;

const CustomerHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const CustomerAvatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${props => props.theme.colors.background};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
`;

const CustomerActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`;

const ActionButton = styled.button`
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  
  background: ${props => 
    props.variant === 'view' ? 'linear-gradient(145deg, #17a2b8, #138496)' :
    props.variant === 'edit' ? 'linear-gradient(145deg, #ffc107, #e0a800)' :
    props.variant === 'delete' ? 'linear-gradient(145deg, #dc3545, #c82333)' :
    'linear-gradient(145deg, #6c757d, #5a6268)'
  };
  
  color: white;

  &:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: ${props => props.theme.shadows.medium};
  }
`;

const CustomerName = styled.h3`
  font-size: ${props => props.theme.fonts.sizes.large};
  font-weight: 600;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const CustomerInfo = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`;

const InfoItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  font-size: ${props => props.theme.fonts.sizes.medium};
  color: ${props => props.theme.colors.textSecondary};
`;

const InfoIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  font-size: 16px;
`;

const CustomerStats = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.lg};
  padding-top: ${props => props.theme.spacing.lg};
  border-top: 1px solid rgba(0, 0, 0, 0.1);
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatValue = styled.div`
  font-size: ${props => props.theme.fonts.sizes.large};
  font-weight: 600;
  color: ${props => props.color || props.theme.colors.textPrimary};
`;

const StatLabel = styled.div`
  font-size: ${props => props.theme.fonts.sizes.small};
  color: ${props => props.theme.colors.textSecondary};
  margin-top: ${props => props.theme.spacing.xs};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xxl};
  color: ${props => props.theme.colors.textSecondary};
  font-size: ${props => props.theme.fonts.sizes.large};
`;

function CustomersPage({ onLogout }) {
  const [customers, setCustomers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // محاكاة جلب البيانات
    const fetchCustomers = async () => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setCustomers([
        {
          id: 1,
          name: 'أحمد محمد علي',
          phone: '07901234567',
          address: 'بغداد - الكرادة',
          notes: 'عميل مميز',
          socialMedia: '@ahmed_mohammed',
          totalOrders: 5,
          totalDebt: 150000,
          lastOrderDate: '2024-12-15'
        },
        {
          id: 2,
          name: 'فاطمة حسن',
          phone: '07801234567',
          address: 'بغداد - الجادرية',
          notes: '',
          socialMedia: '',
          totalOrders: 3,
          totalDebt: 0,
          lastOrderDate: '2024-12-10'
        },
        {
          id: 3,
          name: 'محمد علي',
          phone: '07701234567',
          address: 'بغداد - المنصور',
          notes: 'يفضل الاتصال مساءً',
          socialMedia: '@mohammed_ali',
          totalOrders: 8,
          totalDebt: 75000,
          lastOrderDate: '2024-12-18'
        }
      ]);
      
      setLoading(false);
    };

    fetchCustomers();
  }, []);

  const filteredCustomers = customers.filter(customer =>
    customer.name.includes(searchTerm) ||
    customer.phone.includes(searchTerm) ||
    customer.address.includes(searchTerm)
  );

  const handleViewCustomer = (customer) => {
    alert(`عرض تفاصيل العميل: ${customer.name}`);
  };

  const handleEditCustomer = (customer) => {
    alert(`تعديل العميل: ${customer.name}`);
  };

  const handleDeleteCustomer = (customer) => {
    if (window.confirm(`هل أنت متأكد من حذف العميل: ${customer.name}؟`)) {
      setCustomers(customers.filter(c => c.id !== customer.id));
    }
  };

  const handleAddCustomer = () => {
    alert('إضافة عميل جديد');
  };

  const getCustomerInitials = (name) => {
    const names = name.split(' ');
    return names.length >= 2 ? names[0][0] + names[1][0] : name[0];
  };

  if (loading) {
    return (
      <PageContainer>
        <Sidebar onLogout={onLogout} />
        <MainContent>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '50vh',
            fontSize: '24px',
            color: 'white'
          }}>
            جاري تحميل العملاء...
          </div>
        </MainContent>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <Sidebar onLogout={onLogout} />
      <MainContent>
        <Header>
          <HeaderTitle>
            <FaUser />
            إدارة العملاء
          </HeaderTitle>
          <HeaderActions>
            <SearchInput
              type="text"
              placeholder="البحث عن عميل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <AddButton onClick={handleAddCustomer}>
              <FaPlus />
              إضافة عميل
            </AddButton>
          </HeaderActions>
        </Header>

        {filteredCustomers.length === 0 ? (
          <EmptyState>
            {searchTerm ? 'لا توجد نتائج للبحث' : 'لا يوجد عملاء مسجلين'}
          </EmptyState>
        ) : (
          <CustomersGrid>
            {filteredCustomers.map(customer => (
              <CustomerCard key={customer.id}>
                <CustomerHeader>
                  <CustomerAvatar>
                    {getCustomerInitials(customer.name)}
                  </CustomerAvatar>
                  <CustomerActions>
                    <ActionButton 
                      variant="view" 
                      onClick={() => handleViewCustomer(customer)}
                      title="عرض التفاصيل"
                    >
                      <FaEye />
                    </ActionButton>
                    <ActionButton 
                      variant="edit" 
                      onClick={() => handleEditCustomer(customer)}
                      title="تعديل"
                    >
                      <FaEdit />
                    </ActionButton>
                    <ActionButton 
                      variant="delete" 
                      onClick={() => handleDeleteCustomer(customer)}
                      title="حذف"
                    >
                      <FaTrash />
                    </ActionButton>
                  </CustomerActions>
                </CustomerHeader>

                <CustomerName>{customer.name}</CustomerName>

                <CustomerInfo>
                  <InfoItem>
                    <InfoIcon><FaPhone /></InfoIcon>
                    {customer.phone}
                  </InfoItem>
                  <InfoItem>
                    <InfoIcon><FaMapMarkerAlt /></InfoIcon>
                    {customer.address}
                  </InfoItem>
                  {customer.notes && (
                    <InfoItem>
                      <InfoIcon>📝</InfoIcon>
                      {customer.notes}
                    </InfoItem>
                  )}
                </CustomerInfo>

                <CustomerStats>
                  <StatItem>
                    <StatValue>{customer.totalOrders}</StatValue>
                    <StatLabel>إجمالي الطلبات</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue color={customer.totalDebt > 0 ? '#dc3545' : '#28a745'}>
                      {customer.totalDebt.toLocaleString()}
                    </StatValue>
                    <StatLabel>الديون (دينار)</StatLabel>
                  </StatItem>
                </CustomerStats>
              </CustomerCard>
            ))}
          </CustomersGrid>
        )}
      </MainContent>
    </PageContainer>
  );
}

export default CustomersPage;
