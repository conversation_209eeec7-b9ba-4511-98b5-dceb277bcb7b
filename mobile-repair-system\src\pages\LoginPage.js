import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON>ser, FaLock, FaMobile, FaEye, FaEyeSlash } from 'react-icons/fa';

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: ${props => props.theme.colors.background};
  padding: ${props => props.theme.spacing.lg};
`;

const LoginCard = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xxl};
  width: 100%;
  max-width: 450px;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  }
`;

const LogoContainer = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
  position: relative;
  z-index: 1;
`;

const LogoIcon = styled(FaMobile)`
  font-size: 64px;
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  animation: pulse 2s infinite;

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fonts.sizes.xxlarge};
  font-weight: 700;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.sm};
  position: relative;
  z-index: 1;
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fonts.sizes.medium};
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: ${props => props.theme.spacing.xl};
  position: relative;
  z-index: 1;
`;

const FormContainer = styled.form`
  position: relative;
  z-index: 1;
`;

const InputGroup = styled.div`
  position: relative;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const InputIcon = styled.div`
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textSecondary};
  font-size: 18px;
  z-index: 2;
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md} 50px ${props => props.theme.spacing.md} ${props => props.theme.spacing.md};
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: ${props => props.theme.borderRadius.medium};
  background: rgba(255, 255, 255, 0.9);
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  transition: all 0.3s ease;
  text-align: right;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 1);
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
    text-align: right;
  }
`;

const PasswordToggle = styled.button`
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 18px;
  cursor: pointer;
  z-index: 2;
  transition: color 0.3s ease;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const LoginButton = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.background};
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.large};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: ${props => props.theme.shadows.medium};
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.large};
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const ErrorMessage = styled.div`
  background: linear-gradient(145deg, #dc3545, #e83e8c);
  color: white;
  padding: ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.medium};
  margin-bottom: ${props => props.theme.spacing.lg};
  text-align: center;
  font-weight: 500;
  animation: shake 0.5s ease-in-out;

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-left: 10px;

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

function LoginPage({ onLogin }) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1000));

    // التحقق من بيانات تسجيل الدخول
    if (username === 'abd' && password === 'ZAin1998') {
      onLogin(true);
    } else {
      setError('اسم المستخدم أو كلمة المرور غير صحيحة');
    }

    setLoading(false);
  };

  return (
    <LoginContainer>
      <LoginCard>
        <LogoContainer>
          <LogoIcon />
          <Title>نظام إدارة صيانة الموبايلات</Title>
          <Subtitle>مرحباً بك، يرجى تسجيل الدخول للمتابعة</Subtitle>
        </LogoContainer>

        <FormContainer onSubmit={handleSubmit}>
          {error && <ErrorMessage>{error}</ErrorMessage>}
          
          <InputGroup>
            <InputIcon>
              <FaUser />
            </InputIcon>
            <Input
              type="text"
              placeholder="اسم المستخدم"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              disabled={loading}
            />
          </InputGroup>

          <InputGroup>
            <InputIcon>
              <FaLock />
            </InputIcon>
            <Input
              type={showPassword ? 'text' : 'password'}
              placeholder="كلمة المرور"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
            />
            <PasswordToggle
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={loading}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </PasswordToggle>
          </InputGroup>

          <LoginButton type="submit" disabled={loading}>
            {loading ? (
              <>
                جاري تسجيل الدخول
                <LoadingSpinner />
              </>
            ) : (
              'تسجيل الدخول'
            )}
          </LoginButton>
        </FormContainer>
      </LoginCard>
    </LoginContainer>
  );
}

export default LoginPage;
