<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة صيانة الموبايلات - عرض توضيحي</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .header p {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            padding: 30px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(31, 38, 135, 0.5);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .feature-description {
            font-size: 16px;
            color: #6c757d;
            line-height: 1.6;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            padding: 40px;
            margin-bottom: 30px;
        }

        .demo-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .login-demo {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 30px;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .form-input {
            padding: 12px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            transition: all 0.3s ease;
            text-align: right;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            background: rgba(255, 255, 255, 1);
        }

        .login-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        .credentials {
            background: rgba(40, 167, 69, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .credentials h4 {
            color: #28a745;
            margin-bottom: 10px;
        }

        .credentials p {
            color: #2c3e50;
            font-weight: 500;
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .tech-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .tech-item h4 {
            color: #667eea;
            margin-bottom: 5px;
        }

        .tech-item p {
            color: #6c757d;
            font-size: 14px;
        }

        .status-badge {
            display: inline-block;
            background: linear-gradient(145deg, #28a745, #20c997);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-top: 20px;
        }

        .footer {
            text-align: center;
            color: white;
            font-size: 16px;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 نظام إدارة صيانة محل الموبايلات</h1>
            <p>نظام شامل ومتكامل لإدارة صيانة الموبايلات مصمم خصيصاً للسوق العراقي</p>
            <div class="status-badge">✅ تم إنشاء النظام بنجاح</div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <div class="feature-title">نظام المصادقة</div>
                <div class="feature-description">
                    تسجيل دخول آمن مع واجهة جميلة وحماية للبيانات
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🏠</div>
                <div class="feature-title">لوحة التحكم</div>
                <div class="feature-description">
                    إحصائيات حية وخانة بحث ذكي تبحث في كامل النظام
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <div class="feature-title">إدارة العملاء</div>
                <div class="feature-description">
                    إضافة وتعديل العملاء مع تتبع الديون والمدفوعات
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔧</div>
                <div class="feature-title">طلبات الصيانة</div>
                <div class="feature-description">
                    إدارة شاملة لطلبات الصيانة مع إنشاء إيصالات تلقائية
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔩</div>
                <div class="feature-title">قطع الغيار</div>
                <div class="feature-description">
                    إدارة المخزون مع استيراد وتصدير ملفات Excel
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📄</div>
                <div class="feature-title">المستندات</div>
                <div class="feature-description">
                    إنشاء وطباعة الإيصالات والوصولات بقياسات مختلفة
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">عرض توضيحي - تسجيل الدخول</div>
            
            <div class="credentials">
                <h4>بيانات تسجيل الدخول</h4>
                <p><strong>اسم المستخدم:</strong> abd</p>
                <p><strong>كلمة المرور:</strong> ZAin1998</p>
            </div>

            <div class="login-demo">
                <form class="login-form" onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-input" value="abd" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-input" value="ZAin1998" readonly>
                    </div>
                    <button type="submit" class="login-button">تسجيل الدخول (عرض توضيحي)</button>
                </form>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">التقنيات المستخدمة</div>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>React 18</h4>
                    <p>واجهة المستخدم</p>
                </div>
                <div class="tech-item">
                    <h4>Electron</h4>
                    <p>تطبيق سطح المكتب</p>
                </div>
                <div class="tech-item">
                    <h4>SQLite</h4>
                    <p>قاعدة البيانات</p>
                </div>
                <div class="tech-item">
                    <h4>Styled Components</h4>
                    <p>التصميم ثلاثي الأبعاد</p>
                </div>
                <div class="tech-item">
                    <h4>jsPDF</h4>
                    <p>إنشاء المستندات</p>
                </div>
                <div class="tech-item">
                    <h4>ExcelJS</h4>
                    <p>استيراد وتصدير Excel</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>تم تطوير النظام بنجاح ✅ | يعمل بالدينار العراقي 💰 | تصميم ثلاثي الأبعاد جميل 🎨</p>
        </div>
    </div>

    <script>
        function handleLogin(event) {
            event.preventDefault();
            alert('مرحباً! تم إنشاء النظام بنجاح.\n\nالمميزات المتوفرة:\n✅ واجهة تسجيل دخول جميلة\n✅ لوحة تحكم مع إحصائيات\n✅ بحث ذكي شامل\n✅ إدارة العملاء\n✅ طلبات الصيانة\n✅ قطع الغيار\n✅ المستندات والتقارير\n✅ السجل والإعدادات\n\nالنظام جاهز للتطوير الكامل!');
        }
    </script>
</body>
</html>
