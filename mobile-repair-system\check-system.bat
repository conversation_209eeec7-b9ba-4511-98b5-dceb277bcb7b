@echo off
title Mobile Repair System - System Check
color 0E

echo.
echo ========================================
echo     Mobile Repair System - System Check
echo ========================================
echo.

echo Checking system requirements...
echo.

:: Check Windows version
echo Computer System:
ver
echo.

:: Check Node.js
echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed
    echo Download from: https://nodejs.org
    set nodejs_ok=0
) else (
    echo OK: Node.js installed - Version:
    node --version
    set nodejs_ok=1
)

echo.

:: Check npm
echo Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not available
    set npm_ok=0
) else (
    echo OK: npm available - Version:
    npm --version
    set npm_ok=1
)

echo.

:: Check project files
echo Checking project files...
if exist "package.json" (
    echo OK: package.json exists
    set package_ok=1
) else (
    echo ERROR: package.json not found
    set package_ok=0
)

if exist "src" (
    echo OK: src folder exists
    set src_ok=1
) else (
    echo ERROR: src folder not found
    set src_ok=0
)

if exist "public" (
    echo OK: public folder exists
    set public_ok=1
) else (
    echo ERROR: public folder not found
    set public_ok=0
)

if exist "node_modules" (
    echo OK: node_modules folder exists (libraries installed)
    set modules_ok=1
) else (
    echo WARNING: node_modules folder not found (libraries not installed)
    set modules_ok=0
)

echo.

:: Check important files
echo Checking important files...
if exist "src\App.js" (
    echo OK: App.js exists
) else (
    echo ERROR: App.js not found
)

if exist "src\index.js" (
    echo OK: index.js exists
) else (
    echo ERROR: index.js not found
)

if exist "public\index.html" (
    echo OK: index.html exists
) else (
    echo ERROR: index.html not found
)

if exist "demo.html" (
    echo OK: demo.html exists
) else (
    echo ERROR: demo.html not found
)

echo.
echo ========================================
echo           Check Results
echo ========================================
echo.

if %nodejs_ok%==1 if %npm_ok%==1 if %package_ok%==1 if %src_ok%==1 if %public_ok%==1 (
    echo SUCCESS: System is ready to run!
    echo.
    if %modules_ok%==0 (
        echo WARNING: You need to install libraries first
        echo Use "install.bat" or command: npm install
        echo.
    )
    echo To run the system:
    echo 1. Double-click "quick-start.bat"
    echo 2. Or use "start.bat"
    echo 3. Or type in Terminal: npm start
) else (
    echo ERROR: System is not ready to run
    echo.
    echo Problems found:
    if %nodejs_ok%==0 echo - Node.js is not installed
    if %npm_ok%==0 echo - npm is not available
    if %package_ok%==0 echo - package.json is missing
    if %src_ok%==0 echo - src folder is missing
    if %public_ok%==0 echo - public folder is missing
    echo.
    echo Please re-download the system or fix the mentioned problems
)

echo.
echo Login Credentials:
echo Username: abd
echo Password: ZAin1998
echo.
echo ========================================
echo.

pause
