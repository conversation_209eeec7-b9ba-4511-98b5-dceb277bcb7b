# 🔧 دليل حل المشاكل الشائعة

## 🚨 المشكلة: ملف start.bat يفتح ويغلق مباشرة

### **الحلول:**

#### **الحل الأول: استخدم الملفات الجديدة**
1. انقر نقراً مزدوجاً على `فحص_النظام.bat` لفحص المشاكل
2. انقر نقراً مزدوجاً على `تشغيل_سريع.bat` للتشغيل المباشر

#### **الحل الثاني: تشغيل من Command Prompt**
1. اضغط `Win + R`
2. اكتب `cmd` واضغط Enter
3. انتقل للمجلد:
   ```cmd
   cd "c:\Users\<USER>\Desktop\برنامج الصيانة 206\mobile-repair-system"
   ```
4. شغل الأمر:
   ```cmd
   start.bat
   ```

#### **الحل الثالث: التشغيل اليدوي**
1. افتح Command Prompt في المجلد
2. اكتب:
   ```cmd
   npm start
   ```

---

## 🚨 المشكلة: "node is not recognized"

### **الحل:**
1. حمل Node.js من: https://nodejs.org
2. اختر الإصدار LTS (الموصى به)
3. ثبته مع الإعدادات الافتراضية
4. أعد تشغيل الكمبيوتر
5. جرب مرة أخرى

---

## 🚨 المشكلة: "npm install فشل"

### **الحلول:**

#### **الحل الأول:**
```cmd
npm cache clean --force
npm install
```

#### **الحل الثاني:**
```cmd
npm install --legacy-peer-deps
```

#### **الحل الثالث:**
1. احذف مجلد `node_modules` إن وُجد
2. احذف ملف `package-lock.json` إن وُجد
3. شغل:
   ```cmd
   npm install
   ```

---

## 🚨 المشكلة: النظام لا يفتح في المتصفح

### **الحلول:**

#### **الحل الأول:**
1. انتظر رسالة "compiled successfully"
2. افتح المتصفح يدوياً
3. انتقل إلى: `http://localhost:3000`

#### **الحل الثاني:**
```cmd
npm start
```
ثم في متصفح جديد:
```
http://localhost:3000
```

---

## 🚨 المشكلة: خطأ في المنفذ (Port)

### **الحل:**
```cmd
npx kill-port 3000
npm start
```

---

## 🚨 المشكلة: الخطوط العربية لا تظهر

### **الحل:**
1. تأكد من اتصال الإنترنت
2. أعد تحميل الصفحة (F5)
3. امسح cache المتصفح

---

## 🚨 المشكلة: النظام بطيء

### **الحلول:**
1. أغلق البرامج الأخرى
2. تأكد من وجود 4GB RAM على الأقل
3. استخدم متصفح Chrome أو Edge

---

## 📋 خطوات التشغيل الصحيحة

### **للمرة الأولى:**
1. `فحص_النظام.bat` - للتأكد من المتطلبات
2. `install.bat` - لتثبيت المكتبات
3. `تشغيل_سريع.bat` - لتشغيل النظام

### **للاستخدام اليومي:**
- `تشغيل_سريع.bat` فقط

---

## 🔑 بيانات تسجيل الدخول

```
اسم المستخدم: abd
كلمة المرور: ZAin1998
```

---

## 📞 إذا لم تحل المشكلة

### **جرب هذه الخطوات:**

1. **أعد تحميل النظام كاملاً**
2. **تأكد من تثبيت Node.js الإصدار الأحدث**
3. **شغل Command Prompt كمدير (Run as Administrator)**
4. **تأكد من عدم وجود برامج حماية تمنع التشغيل**

### **للعرض السريع:**
- انقر على `demo.html` لرؤية النظام بدون تثبيت

---

## ✅ علامات النجاح

عند نجاح التشغيل ستشاهد:
- رسالة "compiled successfully" في Terminal
- فتح المتصفح تلقائياً على localhost:3000
- ظهور صفحة تسجيل الدخول الجميلة

---

## 🎯 نصائح مهمة

1. **لا تغلق نافذة Command Prompt أثناء التشغيل**
2. **تأكد من اتصال الإنترنت عند التثبيت**
3. **استخدم متصفح حديث (Chrome, Edge, Firefox)**
4. **تأكد من وجود مساحة كافية على القرص الصلب**

---

**النظام تم إنشاؤه بنجاح ✅ وجاهز للاستخدام! 🚀**
