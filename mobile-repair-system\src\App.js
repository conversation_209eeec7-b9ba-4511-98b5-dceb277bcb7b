import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import styled, { ThemeProvider, createGlobalStyle } from 'styled-components';

// استيراد المكونات
import LoginPage from './pages/LoginPage';
import Dashboard from './pages/Dashboard';
import CustomersPage from './pages/CustomersPage';
import OrdersPage from './pages/OrdersPage';
import AddOrderPage from './pages/AddOrderPage';
import SparePartsPage from './pages/SparePartsPage';
import DocumentsPage from './pages/DocumentsPage';
import ReportsPage from './pages/ReportsPage';
import ActivityLogPage from './pages/ActivityLogPage';
import SettingsPage from './pages/SettingsPage';

// الثيم الرئيسي
const theme = {
  colors: {
    primary: '#667eea',
    secondary: '#764ba2',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40',
    white: '#ffffff',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    cardBackground: 'rgba(255, 255, 255, 0.95)',
    textPrimary: '#2c3e50',
    textSecondary: '#6c757d'
  },
  fonts: {
    primary: "'Cairo', sans-serif",
    sizes: {
      small: '12px',
      medium: '14px',
      large: '16px',
      xlarge: '18px',
      xxlarge: '24px'
    }
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px'
  },
  borderRadius: {
    small: '8px',
    medium: '12px',
    large: '16px',
    xlarge: '20px'
  },
  shadows: {
    small: '0 2px 8px rgba(0, 0, 0, 0.1)',
    medium: '0 4px 16px rgba(0, 0, 0, 0.15)',
    large: '0 8px 32px rgba(31, 38, 135, 0.37)',
    button: '20px 20px 60px #bebebe, -20px -20px 60px #ffffff'
  }
};

// الأنماط العامة
const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: ${props => props.theme.fonts.primary};
    direction: rtl;
    text-align: right;
    background: ${props => props.theme.colors.background};
    min-height: 100vh;
    overflow-x: hidden;
    color: ${props => props.theme.colors.textPrimary};
  }

  #root {
    min-height: 100vh;
  }

  /* تحسين شريط التمرير */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.background};
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  }

  /* تحسين الخطوط العربية */
  .arabic-text {
    font-family: ${props => props.theme.fonts.primary};
    font-weight: 400;
    line-height: 1.6;
  }

  /* أنماط الأزرار ثلاثية الأبعاد */
  .btn-3d {
    background: linear-gradient(145deg, #e6e6e6, #ffffff);
    box-shadow: ${props => props.theme.shadows.button};
    border: none;
    border-radius: ${props => props.theme.borderRadius.medium};
    padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
    font-family: ${props => props.theme.fonts.primary};
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-size: ${props => props.theme.fonts.sizes.medium};
  }

  .btn-3d:hover {
    transform: translateY(-2px);
    box-shadow: 25px 25px 70px #bebebe, -25px -25px 70px #ffffff;
  }

  .btn-3d:active {
    transform: translateY(0);
    box-shadow: inset 20px 20px 60px #bebebe, inset -20px -20px 60px #ffffff;
  }

  .btn-primary {
    background: ${props => props.theme.colors.background};
    color: white;
  }

  .btn-success {
    background: linear-gradient(145deg, #28a745, #20c997);
    color: white;
  }

  .btn-warning {
    background: linear-gradient(145deg, #ffc107, #fd7e14);
    color: white;
  }

  .btn-danger {
    background: linear-gradient(145deg, #dc3545, #e83e8c);
    color: white;
  }

  /* أنماط البطاقات */
  .card-3d {
    background: ${props => props.theme.colors.cardBackground};
    backdrop-filter: blur(10px);
    border-radius: ${props => props.theme.borderRadius.xlarge};
    box-shadow: ${props => props.theme.shadows.large};
    border: 1px solid rgba(255, 255, 255, 0.18);
    padding: ${props => props.theme.spacing.lg};
    margin: ${props => props.theme.spacing.md};
    transition: all 0.3s ease;
  }

  .card-3d:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
  }

  /* أنماط حقول الإدخال */
  .input-3d {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: ${props => props.theme.borderRadius.medium};
    padding: ${props => props.theme.spacing.md};
    font-family: ${props => props.theme.fonts.primary};
    font-size: ${props => props.theme.fonts.sizes.medium};
    transition: all 0.3s ease;
    width: 100%;
    margin: ${props => props.theme.spacing.sm} 0;
  }

  .input-3d:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 1);
  }

  /* أنماط الجداول */
  .table-3d {
    background: ${props => props.theme.colors.cardBackground};
    border-radius: ${props => props.theme.borderRadius.medium};
    overflow: hidden;
    box-shadow: ${props => props.theme.shadows.large};
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
  }

  .table-3d th {
    background: ${props => props.theme.colors.background};
    color: white;
    padding: ${props => props.theme.spacing.md};
    font-weight: 600;
    text-align: center;
  }

  .table-3d td {
    padding: ${props => props.theme.spacing.md};
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
  }

  .table-3d tr:hover {
    background: rgba(102, 126, 234, 0.1);
  }
`;

const AppContainer = styled.div`
  min-height: 100vh;
  background: ${props => props.theme.colors.background};
`;

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // فحص حالة تسجيل الدخول من localStorage
    const authStatus = localStorage.getItem('isAuthenticated');
    if (authStatus === 'true') {
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  const handleLogin = (success) => {
    if (success) {
      setIsAuthenticated(true);
      localStorage.setItem('isAuthenticated', 'true');
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem('isAuthenticated');
  };

  if (loading) {
    return (
      <ThemeProvider theme={theme}>
        <GlobalStyle />
        <AppContainer>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100vh',
            fontSize: '24px',
            color: 'white'
          }}>
            جاري التحميل...
          </div>
        </AppContainer>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyle />
      <AppContainer>
        <Router>
          <Routes>
            <Route 
              path="/login" 
              element={
                !isAuthenticated ? 
                <LoginPage onLogin={handleLogin} /> : 
                <Navigate to="/dashboard" replace />
              } 
            />
            <Route 
              path="/dashboard" 
              element={
                isAuthenticated ? 
                <Dashboard onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/customers" 
              element={
                isAuthenticated ? 
                <CustomersPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/orders" 
              element={
                isAuthenticated ? 
                <OrdersPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/add-order" 
              element={
                isAuthenticated ? 
                <AddOrderPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/spare-parts" 
              element={
                isAuthenticated ? 
                <SparePartsPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/documents" 
              element={
                isAuthenticated ? 
                <DocumentsPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/reports" 
              element={
                isAuthenticated ? 
                <ReportsPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/activity-log" 
              element={
                isAuthenticated ? 
                <ActivityLogPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/settings" 
              element={
                isAuthenticated ? 
                <SettingsPage onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
              } 
            />
            <Route 
              path="/" 
              element={
                <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
              } 
            />
          </Routes>
        </Router>
      </AppContainer>
    </ThemeProvider>
  );
}

export default App;
