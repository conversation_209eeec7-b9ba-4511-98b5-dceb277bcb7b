@echo off
chcp 65001 >nul
title Mobile Repair System
color 0A

:menu
cls
echo.
echo ========================================
echo    Mobile Repair Management System
echo ========================================
echo.

echo [1] Install Required Libraries
echo [2] Run System
echo [3] Build for Production
echo [4] Demo
echo [5] Exit
echo.

set /p choice="Choose option number: "

if "%choice%"=="1" goto install
if "%choice%"=="2" goto run
if "%choice%"=="3" goto build
if "%choice%"=="4" goto demo
if "%choice%"=="5" goto exit
echo Invalid choice! Please choose number from 1 to 5
pause
goto menu

:install
echo.
echo Installing libraries...
call npm install
if errorlevel 1 (
    echo.
    echo Installation failed!
    echo Make sure Node.js is installed from https://nodejs.org
    pause
    goto menu
)
echo.
echo Libraries installed successfully!
pause
goto menu

:run
echo.
echo Starting system...
echo System will open in browser...
echo.
echo Login credentials:
echo Username: abd
echo Password: ZAin1998
echo.
timeout /t 3 /nobreak >nul
start http://localhost:3000
call npm start
if errorlevel 1 (
    echo.
    echo System startup failed!
    echo Make sure to install libraries first (option 1)
    pause
)
goto menu

:build
echo.
echo Building system for production...
call npm run build
call npm run electron-pack
echo.
echo System built successfully!
echo You can find installer in dist folder
pause
goto menu

:demo
echo.
echo Opening demo...
if exist "demo.html" (
    start demo.html
    echo Demo opened in browser
) else (
    echo Demo file not found!
)
pause
goto menu

:exit
echo.
echo Thank you for using the system!
echo System created successfully!
pause
exit
