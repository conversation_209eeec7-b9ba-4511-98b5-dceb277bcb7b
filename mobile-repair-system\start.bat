@echo off
chcp 65001 >nul
title نظام إدارة صيانة الموبايلات
color 0A

:menu
cls
echo.
echo ========================================
echo    نظام إدارة صيانة الموبايلات
echo ========================================
echo.

echo [1] تثبيت المكتبات المطلوبة
echo [2] تشغيل النظام
echo [3] بناء النظام للإنتاج
echo [4] عرض توضيحي
echo [5] خروج
echo.

set /p choice="اختر رقم العملية: "

if "%choice%"=="1" goto install
if "%choice%"=="2" goto run
if "%choice%"=="3" goto build
if "%choice%"=="4" goto demo
if "%choice%"=="5" goto exit
echo خيار غير صحيح! يرجى اختيار رقم من 1 إلى 5
pause
goto menu

:install
echo.
echo جاري تثبيت المكتبات...
call npm install
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء التثبيت!
    echo تأكد من تثبيت Node.js من https://nodejs.org
    pause
    goto menu
)
echo.
echo تم تثبيت المكتبات بنجاح!
pause
goto menu

:run
echo.
echo جاري تشغيل النظام...
echo سيتم فتح النظام في المتصفح...
echo.
echo بيانات تسجيل الدخول:
echo اسم المستخدم: abd
echo كلمة المرور: ZAin1998
echo.
timeout /t 3 /nobreak >nul
start http://localhost:3000
call npm start
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل النظام!
    echo تأكد من تثبيت المكتبات أولاً (اختيار رقم 1)
    pause
)
goto menu

:build
echo.
echo جاري بناء النظام للإنتاج...
call npm run build
call npm run electron-pack
echo.
echo تم بناء النظام بنجاح!
echo يمكنك العثور على ملف التثبيت في مجلد dist
pause
goto menu

:demo
echo.
echo فتح العرض التوضيحي...
if exist "demo.html" (
    start demo.html
    echo تم فتح العرض التوضيحي في المتصفح
) else (
    echo ملف العرض التوضيحي غير موجود!
)
pause
goto menu

:exit
echo.
echo شكراً لاستخدام النظام!
echo تم إنشاء النظام بنجاح ✅
pause
exit
