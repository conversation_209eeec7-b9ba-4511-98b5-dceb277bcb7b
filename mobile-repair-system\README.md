# نظام إدارة صيانة محل الموبايلات

نظام شامل لإدارة صيانة محل الموبايلات مبني بتقنيات حديثة ومصمم خصيصاً للسوق العراقي.

## المميزات الرئيسية

### 🔐 نظام المصادقة
- تسجيل دخول آمن
- اسم المستخدم: `abd`
- كلمة المرور: `ZAin1998`

### 🏠 لوحة التحكم الرئيسية
- إحصائيات حية شاملة
- خانة بحث ذكي تبحث في كامل النظام
- واجهة عربية جميلة مع أزرار ثلاثية الأبعاد
- خلفيات متدرجة جذابة

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- عرض تفاصيل كاملة لكل عميل
- تتبع الديون والمدفوعات
- إدارة وصولات التسديد
- عرض تاريخ الطلبات لكل عميل

### 🔧 إدارة طلبات الصيانة
- إضافة طلبات صيانة جديدة
- اختيار العميل من قائمة أو إضافة عميل جديد
- تحديد نوع الهاتف من الشركات المصنعة
- وصف مفصل للمشكلة
- تحديد السعر وطريقة الدفع (نقداً، جزئي، آجل)
- إنشاء إيصالات تلقائية بمقاس A5
- تتبع حالة الطلب (قيد الصيانة، مكتمل، فشل، بانتظار قطع غيار، تم التسليم)

### 🔩 إدارة قطع الغيار
- إضافة وتعديل قطع الغيار
- تتبع المخزون والأسعار
- استيراد وتصدير ملفات Excel
- إنشاء باركود لكل قطعة
- تقارير المخزون

### 📄 نظام المستندات
- إنشاء إيصالات صيانة تلقائية
- وصولات تسديد الديون
- طباعة بقياسات مختلفة (A4, A5, طابعة حرارية)
- تخزين تلقائي لجميع المستندات
- إمكانية إعادة طباعة أي مستند

### 📊 التقارير والإحصائيات
- تقارير شاملة عن الطلبات والعملاء
- تقارير المبيعات والديون
- إحصائيات الأداء
- تصدير التقارير إلى Excel و PDF

### 📝 سجل العمليات
- تتبع جميع التغييرات في النظام
- سجل المستخدمين والعمليات
- بحث متقدم في السجل
- تاريخ ووقت كل عملية

### ⚙️ الإعدادات
- تخصيص معلومات المركز
- تغيير الشعار والألوان
- إعدادات الطباعة
- إدارة المستخدمين
- نسخ احتياطية واستعادة البيانات

## التقنيات المستخدمة

- **Frontend**: React 18 مع Styled Components
- **Desktop**: Electron للتطبيق المكتبي
- **Database**: SQLite لقاعدة البيانات المحلية
- **UI/UX**: تصميم ثلاثي الأبعاد مع خطوط عربية جميلة
- **Icons**: React Icons
- **PDF Generation**: jsPDF
- **Excel**: ExcelJS
- **QR Codes**: qrcode library

## متطلبات النظام

- Windows 10 أو أحدث
- 4 GB RAM كحد أدنى
- 500 MB مساحة فارغة على القرص الصلب
- دقة شاشة 1280x720 كحد أدنى

## التثبيت والتشغيل

### للمطورين:

```bash
# تثبيت المكتبات
npm install

# تشغيل النظام في وضع التطوير
npm run electron-dev

# بناء النظام للإنتاج
npm run build
npm run electron-pack
```

### للمستخدمين:
1. تحميل ملف التثبيت من قسم Releases
2. تشغيل ملف التثبيت
3. اتباع تعليمات التثبيت
4. تشغيل النظام من سطح المكتب

## العملة المستخدمة

النظام يعمل بالدينار العراقي كعملة افتراضية مع إمكانية التخصيص.

## الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال pull request.

---

**تم تطويره بـ ❤️ للسوق العراقي**
