@echo off
chcp 65001 >nul
title فحص النظام - نظام إدارة صيانة الموبايلات
color 0E

echo.
echo ========================================
echo        فحص النظام والمتطلبات
echo ========================================
echo.

echo جاري فحص المتطلبات...
echo.

:: فحص إصدار Windows
echo 🖥️  نظام التشغيل:
ver
echo.

:: فحص وجود Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميله من: https://nodejs.org
    set nodejs_ok=0
) else (
    echo ✅ Node.js مثبت - الإصدار:
    node --version
    set nodejs_ok=1
)

echo.

:: فحص وجود npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر
    set npm_ok=0
) else (
    echo ✅ npm متوفر - الإصدار:
    npm --version
    set npm_ok=1
)

echo.

:: فحص وجود المجلد
echo 🔍 فحص ملفات المشروع...
if exist "package.json" (
    echo ✅ ملف package.json موجود
    set package_ok=1
) else (
    echo ❌ ملف package.json غير موجود
    set package_ok=0
)

if exist "src" (
    echo ✅ مجلد src موجود
    set src_ok=1
) else (
    echo ❌ مجلد src غير موجود
    set src_ok=0
)

if exist "public" (
    echo ✅ مجلد public موجود
    set public_ok=1
) else (
    echo ❌ مجلد public غير موجود
    set public_ok=0
)

if exist "node_modules" (
    echo ✅ مجلد node_modules موجود (المكتبات مثبتة)
    set modules_ok=1
) else (
    echo ⚠️  مجلد node_modules غير موجود (المكتبات غير مثبتة)
    set modules_ok=0
)

echo.

:: فحص الملفات المهمة
echo 🔍 فحص الملفات المهمة...
if exist "src\App.js" (
    echo ✅ ملف App.js موجود
) else (
    echo ❌ ملف App.js غير موجود
)

if exist "src\index.js" (
    echo ✅ ملف index.js موجود
) else (
    echo ❌ ملف index.js غير موجود
)

if exist "public\index.html" (
    echo ✅ ملف index.html موجود
) else (
    echo ❌ ملف index.html غير موجود
)

if exist "demo.html" (
    echo ✅ ملف العرض التوضيحي موجود
) else (
    echo ❌ ملف العرض التوضيحي غير موجود
)

echo.
echo ========================================
echo           نتيجة الفحص
echo ========================================
echo.

if %nodejs_ok%==1 if %npm_ok%==1 if %package_ok%==1 if %src_ok%==1 if %public_ok%==1 (
    echo ✅ النظام جاهز للتشغيل!
    echo.
    if %modules_ok%==0 (
        echo ⚠️  تحتاج لتثبيت المكتبات أولاً
        echo استخدم ملف "install.bat" أو الأمر: npm install
        echo.
    )
    echo للتشغيل:
    echo 1. انقر على ملف "تشغيل_سريع.bat"
    echo 2. أو استخدم ملف "start.bat"
    echo 3. أو اكتب في Terminal: npm start
) else (
    echo ❌ النظام غير جاهز للتشغيل
    echo.
    echo المشاكل المكتشفة:
    if %nodejs_ok%==0 echo - Node.js غير مثبت
    if %npm_ok%==0 echo - npm غير متوفر
    if %package_ok%==0 echo - ملف package.json مفقود
    if %src_ok%==0 echo - مجلد src مفقود
    if %public_ok%==0 echo - مجلد public مفقود
    echo.
    echo يرجى إعادة تحميل النظام أو إصلاح المشاكل المذكورة
)

echo.
echo ========================================
echo.

pause
