const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// إنشاء مجلد البيانات إذا لم يكن موجوداً
const dataDir = path.join(__dirname, '../../data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

const dbPath = path.join(dataDir, 'mobile_repair.db');
const db = new Database(dbPath);

// تفعيل الدعم للمفاتيح الخارجية
db.pragma('foreign_keys = ON');

// إنشاء الجداول
function initializeDatabase() {
  // جدول الإعدادات
  db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      shop_name TEXT DEFAULT 'مركز صيانة الموبايلات',
      shop_address TEXT DEFAULT 'العراق - بغداد',
      shop_phone TEXT DEFAULT '07xxxxxxxxx',
      shop_email TEXT DEFAULT '<EMAIL>',
      shop_logo TEXT DEFAULT '',
      currency TEXT DEFAULT 'دينار عراقي',
      username TEXT DEFAULT 'abd',
      password TEXT DEFAULT 'ZAin1998',
      print_format TEXT DEFAULT 'A5',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول العملاء
  db.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT NOT NULL UNIQUE,
      address TEXT,
      notes TEXT,
      social_media TEXT,
      total_debt REAL DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول شركات الهواتف
  db.exec(`
    CREATE TABLE IF NOT EXISTS phone_brands (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول طلبات الصيانة
  db.exec(`
    CREATE TABLE IF NOT EXISTS repair_orders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_number TEXT NOT NULL UNIQUE,
      customer_id INTEGER NOT NULL,
      phone_brand TEXT NOT NULL,
      problem_description TEXT NOT NULL,
      price REAL NOT NULL,
      payment_method TEXT NOT NULL, -- 'cash', 'partial', 'deferred'
      paid_amount REAL DEFAULT 0,
      remaining_amount REAL DEFAULT 0,
      status TEXT DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'failed', 'waiting_parts', 'delivered'
      failure_reason TEXT,
      waiting_parts TEXT,
      delivery_date DATETIME,
      recipient_name TEXT,
      delivery_notes TEXT,
      order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers (id)
    )
  `);

  // جدول قطع الغيار
  db.exec(`
    CREATE TABLE IF NOT EXISTS spare_parts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      purchase_price REAL NOT NULL,
      selling_price REAL NOT NULL,
      quantity INTEGER NOT NULL DEFAULT 0,
      barcode TEXT UNIQUE,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول المستندات
  db.exec(`
    CREATE TABLE IF NOT EXISTS documents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      type TEXT NOT NULL, -- 'repair_receipt', 'payment_receipt'
      reference_id INTEGER NOT NULL,
      document_data TEXT NOT NULL, -- JSON data
      file_path TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول المدفوعات
  db.exec(`
    CREATE TABLE IF NOT EXISTS payments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      customer_id INTEGER NOT NULL,
      order_id INTEGER,
      amount REAL NOT NULL,
      payment_type TEXT NOT NULL, -- 'order_payment', 'debt_payment'
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers (id),
      FOREIGN KEY (order_id) REFERENCES repair_orders (id)
    )
  `);

  // جدول السجل
  db.exec(`
    CREATE TABLE IF NOT EXISTS activity_log (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      action TEXT NOT NULL,
      table_name TEXT NOT NULL,
      record_id INTEGER,
      old_data TEXT,
      new_data TEXT,
      user_name TEXT DEFAULT 'admin',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // إدراج البيانات الافتراضية
  insertDefaultData();
}

function insertDefaultData() {
  // إدراج الإعدادات الافتراضية
  const settingsCount = db.prepare('SELECT COUNT(*) as count FROM settings').get();
  if (settingsCount.count === 0) {
    db.prepare(`
      INSERT INTO settings (shop_name, shop_address, shop_phone, username, password)
      VALUES (?, ?, ?, ?, ?)
    `).run('مركز صيانة الموبايلات', 'العراق - بغداد', '07xxxxxxxxx', 'abd', 'ZAin1998');
  }

  // إدراج شركات الهواتف الافتراضية
  const brandsCount = db.prepare('SELECT COUNT(*) as count FROM phone_brands').get();
  if (brandsCount.count === 0) {
    const brands = ['Samsung', 'iPhone', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo', 'OnePlus', 'Nokia', 'LG', 'Sony'];
    const insertBrand = db.prepare('INSERT INTO phone_brands (name) VALUES (?)');
    
    brands.forEach(brand => {
      try {
        insertBrand.run(brand);
      } catch (error) {
        // تجاهل الأخطاء في حالة وجود البراند مسبقاً
      }
    });
  }
}

// تهيئة قاعدة البيانات
initializeDatabase();

module.exports = {
  db,
  
  // دوال العملاء
  getCustomers: () => {
    return db.prepare('SELECT * FROM customers ORDER BY created_at DESC').all();
  },
  
  getCustomerById: (id) => {
    return db.prepare('SELECT * FROM customers WHERE id = ?').get(id);
  },
  
  addCustomer: (customer) => {
    const stmt = db.prepare(`
      INSERT INTO customers (name, phone, address, notes, social_media)
      VALUES (?, ?, ?, ?, ?)
    `);
    return stmt.run(customer.name, customer.phone, customer.address, customer.notes, customer.social_media);
  },
  
  updateCustomer: (id, customer) => {
    const stmt = db.prepare(`
      UPDATE customers 
      SET name = ?, phone = ?, address = ?, notes = ?, social_media = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    return stmt.run(customer.name, customer.phone, customer.address, customer.notes, customer.social_media, id);
  },
  
  deleteCustomer: (id) => {
    return db.prepare('DELETE FROM customers WHERE id = ?').run(id);
  },

  // دوال الطلبات
  getOrders: () => {
    return db.prepare(`
      SELECT ro.*, c.name as customer_name, c.phone as customer_phone
      FROM repair_orders ro
      JOIN customers c ON ro.customer_id = c.id
      ORDER BY ro.created_at DESC
    `).all();
  },
  
  getOrderById: (id) => {
    return db.prepare(`
      SELECT ro.*, c.name as customer_name, c.phone as customer_phone
      FROM repair_orders ro
      JOIN customers c ON ro.customer_id = c.id
      WHERE ro.id = ?
    `).get(id);
  },
  
  addOrder: (order) => {
    const orderNumber = generateOrderNumber();
    const stmt = db.prepare(`
      INSERT INTO repair_orders (
        order_number, customer_id, phone_brand, problem_description, 
        price, payment_method, paid_amount, remaining_amount, order_date, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const remaining = order.payment_method === 'cash' ? 0 : 
                     order.payment_method === 'partial' ? order.price - order.paid_amount : 
                     order.price;
    
    return stmt.run(
      orderNumber, order.customer_id, order.phone_brand, order.problem_description,
      order.price, order.payment_method, order.paid_amount || 0, remaining,
      order.order_date, order.notes
    );
  },

  // دوال أخرى...
  getPhoneBrands: () => {
    return db.prepare('SELECT * FROM phone_brands ORDER BY name').all();
  },
  
  getSettings: () => {
    return db.prepare('SELECT * FROM settings LIMIT 1').get();
  }
};

function generateOrderNumber() {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  
  const prefix = `${year}${month}${day}`;
  const count = db.prepare('SELECT COUNT(*) as count FROM repair_orders WHERE order_number LIKE ?').get(`${prefix}%`);
  const sequence = String(count.count + 1).padStart(3, '0');
  
  return `${prefix}${sequence}`;
}
