import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FaUser, 
  FaMobile, 
  FaTools, 
  FaMoneyBillWave, 
  FaCalendarAlt, 
  FaStickyNote,
  FaPlus,
  FaSave,
  FaArrowLeft,
  FaPrint
} from 'react-icons/fa';

import Sidebar from '../components/Sidebar';

const PageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: ${props => props.theme.colors.background};
`;

const MainContent = styled.div`
  flex: 1;
  margin-right: 280px;
  padding: ${props => props.theme.spacing.xl};
  transition: margin-right 0.3s ease;

  @media (max-width: 768px) {
    margin-right: 0;
    padding: ${props => props.theme.spacing.lg};
  }
`;

const Header = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

const BackButton = styled.button`
  background: ${props => props.theme.colors.background};
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: ${props => props.theme.spacing.md};
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  font-family: ${props => props.theme.fonts.primary};
  font-weight: 500;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.medium};
  }
`;

const HeaderTitle = styled.h1`
  font-size: ${props => props.theme.fonts.sizes.xxlarge};
  font-weight: 700;
  color: ${props => props.theme.colors.textPrimary};
`;

const FormContainer = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: ${props => props.theme.spacing.xl};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const Label = styled.label`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  font-weight: 600;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.sm};
  font-size: ${props => props.theme.fonts.sizes.medium};
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: ${props => props.theme.borderRadius.medium};
  background: rgba(255, 255, 255, 0.9);
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  transition: all 0.3s ease;
  text-align: right;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 1);
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
    text-align: right;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: ${props => props.theme.borderRadius.medium};
  background: rgba(255, 255, 255, 0.9);
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  transition: all 0.3s ease;
  text-align: right;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 1);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: ${props => props.theme.borderRadius.medium};
  background: rgba(255, 255, 255, 0.9);
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  transition: all 0.3s ease;
  text-align: right;
  resize: vertical;
  min-height: 100px;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 1);
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
    text-align: right;
  }
`;

const PaymentSection = styled.div`
  background: rgba(102, 126, 234, 0.1);
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PaymentTitle = styled.h3`
  font-size: ${props => props.theme.fonts.sizes.large};
  font-weight: 600;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.md};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const PaymentOptions = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PaymentOption = styled.label`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.md};
  background: rgba(255, 255, 255, 0.9);
  border-radius: ${props => props.theme.borderRadius.medium};
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : 'transparent'};

  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
  }
`;

const RadioInput = styled.input`
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  margin-top: ${props => props.theme.spacing.xl};
`;

const Button = styled.button`
  background: ${props => props.variant === 'primary' ? props.theme.colors.background : 
              props.variant === 'success' ? 'linear-gradient(145deg, #28a745, #20c997)' :
              'linear-gradient(145deg, #6c757d, #5a6268)'};
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.xl};
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  box-shadow: ${props => props.theme.shadows.medium};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.large};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const CustomerSection = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  align-items: end;
`;

const AddCustomerButton = styled.button`
  background: linear-gradient(145deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: ${props => props.theme.spacing.md};
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  height: 50px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.medium};
  }
`;

function AddOrderPage({ onLogout }) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [phoneBrands, setPhoneBrands] = useState([]);
  
  const [formData, setFormData] = useState({
    customerId: '',
    customerName: '',
    customerPhone: '',
    phoneBrand: '',
    problemDescription: '',
    price: '',
    paymentMethod: 'cash',
    paidAmount: '',
    orderDate: new Date().toISOString().split('T')[0],
    notes: ''
  });

  useEffect(() => {
    // محاكاة جلب البيانات
    const fetchData = async () => {
      setCustomers([
        { id: 1, name: 'أحمد محمد علي', phone: '07901234567' },
        { id: 2, name: 'فاطمة حسن', phone: '07801234567' },
        { id: 3, name: 'محمد علي', phone: '07701234567' }
      ]);

      setPhoneBrands([
        'Samsung', 'iPhone', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo', 'OnePlus', 'Nokia'
      ]);
    };

    fetchData();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCustomerSelect = (e) => {
    const customerId = e.target.value;
    const customer = customers.find(c => c.id === parseInt(customerId));
    
    setFormData(prev => ({
      ...prev,
      customerId,
      customerName: customer ? customer.name : '',
      customerPhone: customer ? customer.phone : ''
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // محاكاة حفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // إنشاء رقم الطلب
      const orderNumber = generateOrderNumber();
      
      // محاكاة إنشاء الإيصال
      const receiptData = {
        orderNumber,
        ...formData,
        createdAt: new Date().toISOString()
      };

      console.log('Order created:', receiptData);
      
      // إظهار رسالة نجاح
      alert(`تم إنشاء الطلب بنجاح!\nرقم الطلب: ${orderNumber}`);
      
      // إعادة تعيين النموذج
      setFormData({
        customerId: '',
        customerName: '',
        customerPhone: '',
        phoneBrand: '',
        problemDescription: '',
        price: '',
        paymentMethod: 'cash',
        paidAmount: '',
        orderDate: new Date().toISOString().split('T')[0],
        notes: ''
      });

    } catch (error) {
      alert('حدث خطأ أثناء إنشاء الطلب');
    } finally {
      setLoading(false);
    }
  };

  const generateOrderNumber = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const sequence = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
    
    return `${year}${month}${day}${sequence}`;
  };

  const handlePrintReceipt = () => {
    // محاكاة طباعة الإيصال
    alert('سيتم طباعة الإيصال...');
  };

  return (
    <PageContainer>
      <Sidebar onLogout={onLogout} />
      <MainContent>
        <Header>
          <HeaderLeft>
            <BackButton onClick={() => navigate('/dashboard')}>
              <FaArrowLeft />
              العودة
            </BackButton>
            <HeaderTitle>إضافة طلب صيانة جديد</HeaderTitle>
          </HeaderLeft>
        </Header>

        <FormContainer>
          <form onSubmit={handleSubmit}>
            <FormGrid>
              <FormGroup>
                <Label>
                  <FaUser />
                  العميل
                </Label>
                <CustomerSection>
                  <Select
                    name="customerId"
                    value={formData.customerId}
                    onChange={handleCustomerSelect}
                    required
                  >
                    <option value="">اختر العميل</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name} - {customer.phone}
                      </option>
                    ))}
                  </Select>
                  <AddCustomerButton type="button" title="إضافة عميل جديد">
                    <FaPlus />
                  </AddCustomerButton>
                </CustomerSection>
              </FormGroup>

              <FormGroup>
                <Label>
                  <FaMobile />
                  نوع الهاتف
                </Label>
                <Select
                  name="phoneBrand"
                  value={formData.phoneBrand}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">اختر نوع الهاتف</option>
                  {phoneBrands.map(brand => (
                    <option key={brand} value={brand}>
                      {brand}
                    </option>
                  ))}
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>
                  <FaTools />
                  وصف المشكلة (العطل)
                </Label>
                <TextArea
                  name="problemDescription"
                  value={formData.problemDescription}
                  onChange={handleInputChange}
                  placeholder="اكتب وصف مفصل للمشكلة..."
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>
                  <FaMoneyBillWave />
                  السعر (دينار عراقي)
                </Label>
                <Input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>
                  <FaCalendarAlt />
                  تاريخ الطلب
                </Label>
                <Input
                  type="date"
                  name="orderDate"
                  value={formData.orderDate}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>
                  <FaStickyNote />
                  ملاحظات
                </Label>
                <TextArea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="ملاحظات إضافية (اختياري)..."
                />
              </FormGroup>
            </FormGrid>

            <PaymentSection>
              <PaymentTitle>
                <FaMoneyBillWave />
                طريقة الدفع
              </PaymentTitle>
              <PaymentOptions>
                <PaymentOption selected={formData.paymentMethod === 'cash'}>
                  <RadioInput
                    type="radio"
                    name="paymentMethod"
                    value="cash"
                    checked={formData.paymentMethod === 'cash'}
                    onChange={handleInputChange}
                  />
                  نقداً كاملاً
                </PaymentOption>
                <PaymentOption selected={formData.paymentMethod === 'partial'}>
                  <RadioInput
                    type="radio"
                    name="paymentMethod"
                    value="partial"
                    checked={formData.paymentMethod === 'partial'}
                    onChange={handleInputChange}
                  />
                  جزء من المبلغ
                </PaymentOption>
                <PaymentOption selected={formData.paymentMethod === 'deferred'}>
                  <RadioInput
                    type="radio"
                    name="paymentMethod"
                    value="deferred"
                    checked={formData.paymentMethod === 'deferred'}
                    onChange={handleInputChange}
                  />
                  آجل بعد الصيانة
                </PaymentOption>
              </PaymentOptions>

              {formData.paymentMethod === 'partial' && (
                <FormGroup>
                  <Label>
                    <FaMoneyBillWave />
                    المبلغ المدفوع
                  </Label>
                  <Input
                    type="number"
                    name="paidAmount"
                    value={formData.paidAmount}
                    onChange={handleInputChange}
                    placeholder="0"
                    min="0"
                    max={formData.price}
                    required
                  />
                </FormGroup>
              )}
            </PaymentSection>

            <ButtonGroup>
              <Button type="submit" variant="primary" disabled={loading}>
                <FaSave />
                {loading ? 'جاري الحفظ...' : 'حفظ الطلب'}
              </Button>
              <Button type="button" variant="success" onClick={handlePrintReceipt}>
                <FaPrint />
                طباعة الإيصال
              </Button>
              <Button type="button" onClick={() => navigate('/orders')}>
                عرض الطلبات
              </Button>
            </ButtonGroup>
          </form>
        </FormContainer>
      </MainContent>
    </PageContainer>
  );
}

export default AddOrderPage;
