@echo off
chcp 65001 >nul
title نظام إدارة صيانة الموبايلات
color 0A

echo.
echo ========================================
echo    نظام إدارة صيانة الموبايلات
echo ========================================
echo.

echo [1] تثبيت المكتبات المطلوبة
echo [2] تشغيل النظام
echo [3] بناء النظام للإنتاج
echo [4] عرض توضيحي
echo [5] خروج
echo.

set /p choice="اختر رقم العملية: "

if "%choice%"=="1" goto install
if "%choice%"=="2" goto run
if "%choice%"=="3" goto build
if "%choice%"=="4" goto demo
if "%choice%"=="5" goto exit

:install
echo.
echo جاري تثبيت المكتبات...
npm install
echo.
echo تم تثبيت المكتبات بنجاح!
pause
goto start

:run
echo.
echo جاري تشغيل النظام...
echo سيتم فتح النظام في المتصفح...
start http://localhost:3000
npm start
pause
goto start

:build
echo.
echo جاري بناء النظام للإنتاج...
npm run build
npm run electron-pack
echo.
echo تم بناء النظام بنجاح!
echo يمكنك العثور على ملف التثبيت في مجلد dist
pause
goto start

:demo
echo.
echo فتح العرض التوضيحي...
start demo.html
pause
goto start

:exit
echo.
echo شكراً لاستخدام النظام!
exit

:start
cls
goto menu
