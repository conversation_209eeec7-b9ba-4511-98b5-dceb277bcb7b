{"name": "mobile-repair-system", "version": "1.0.0", "description": "نظام إدارة صيانة محل الموبايلات", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "electron-builder", "preelectron-pack": "npm run build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "styled-components": "^5.3.6", "better-sqlite3": "^8.7.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "exceljs": "^4.3.0", "qrcode": "^1.5.3", "react-icons": "^4.7.1", "date-fns": "^2.29.3", "react-select": "^5.7.0", "react-datepicker": "^4.10.0"}, "devDependencies": {"electron": "^22.0.0", "electron-builder": "^23.6.0", "concurrently": "^7.6.0", "wait-on": "^7.0.1"}, "build": {"appId": "com.mobilerepair.app", "productName": "نظام إدارة صيانة الموبايلات", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}