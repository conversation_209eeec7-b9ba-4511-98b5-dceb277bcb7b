import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FaTachometerAlt, 
  FaUsers, 
  FaPlus, 
  FaList, 
  FaCogs, 
  FaFileAlt, 
  FaChartBar, 
  FaHistory, 
  FaSignOutAlt,
  FaMobile,
  FaBars,
  FaTimes
} from 'react-icons/fa';

const SidebarContainer = styled.div`
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  width: ${props => props.collapsed ? '80px' : '280px'};
  background: ${props => props.theme.colors.cardBackground};
  backdrop-filter: blur(15px);
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
  border-left: 1px solid rgba(255, 255, 255, 0.18);
  transition: all 0.3s ease;
  z-index: 1000;
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.primary};
    border-radius: 2px;
  }
`;

const SidebarHeader = styled.div`
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};
  gap: ${props => props.theme.spacing.md};
`;

const LogoIcon = styled(FaMobile)`
  font-size: 32px;
  color: ${props => props.theme.colors.primary};
  animation: pulse 2s infinite;

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
`;

const LogoText = styled.h2`
  font-size: ${props => props.theme.fonts.sizes.large};
  font-weight: 700;
  color: ${props => props.theme.colors.textPrimary};
  opacity: ${props => props.collapsed ? '0' : '1'};
  transition: opacity 0.3s ease;
  white-space: nowrap;
`;

const CollapseButton = styled.button`
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;

  &:hover {
    background: ${props => props.theme.colors.secondary};
    transform: translateY(-50%) scale(1.1);
  }
`;

const MenuList = styled.ul`
  list-style: none;
  padding: ${props => props.theme.spacing.md} 0;
  margin: 0;
`;

const MenuItem = styled.li`
  margin: ${props => props.theme.spacing.sm} 0;
`;

const MenuLink = styled(Link)`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  color: ${props => props.active ? 'white' : props.theme.colors.textPrimary};
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0 ${props => props.theme.borderRadius.medium} ${props => props.theme.borderRadius.medium} 0;
  margin-left: ${props => props.theme.spacing.md};
  position: relative;
  overflow: hidden;
  background: ${props => props.active ? props.theme.colors.background : 'transparent'};

  &:hover {
    background: ${props => props.active ? props.theme.colors.background : 'rgba(102, 126, 234, 0.1)'};
    transform: translateX(-5px);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: ${props => props.theme.colors.primary};
    transform: scaleY(${props => props.active ? '1' : '0'});
    transition: transform 0.3s ease;
  }
`;

const MenuIcon = styled.div`
  font-size: 20px;
  margin-left: ${props => props.collapsed ? '0' : props.theme.spacing.md};
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const MenuText = styled.span`
  font-weight: 500;
  font-size: ${props => props.theme.fonts.sizes.medium};
  opacity: ${props => props.collapsed ? '0' : '1'};
  transition: opacity 0.3s ease;
  white-space: nowrap;
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  width: 100%;
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  background: linear-gradient(145deg, #dc3545, #e83e8c);
  color: white;
  border: none;
  border-radius: 0 ${props => props.theme.borderRadius.medium} ${props => props.theme.borderRadius.medium} 0;
  margin: ${props => props.theme.spacing.lg} ${props => props.theme.spacing.md} ${props => props.theme.spacing.md} ${props => props.theme.spacing.md};
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: ${props => props.theme.fonts.primary};
  font-weight: 500;

  &:hover {
    background: linear-gradient(145deg, #e83e8c, #dc3545);
    transform: translateX(-5px);
  }
`;

const menuItems = [
  { path: '/dashboard', icon: FaTachometerAlt, text: 'لوحة التحكم' },
  { path: '/add-order', icon: FaPlus, text: 'إضافة صيانة' },
  { path: '/orders', icon: FaList, text: 'عرض الطلبات' },
  { path: '/customers', icon: FaUsers, text: 'العملاء' },
  { path: '/spare-parts', icon: FaCogs, text: 'قطع الغيار' },
  { path: '/documents', icon: FaFileAlt, text: 'المستندات' },
  { path: '/reports', icon: FaChartBar, text: 'التقارير' },
  { path: '/activity-log', icon: FaHistory, text: 'السجل' },
  { path: '/settings', icon: FaCogs, text: 'الإعدادات' }
];

function Sidebar({ onLogout }) {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  const handleLogout = () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      onLogout();
    }
  };

  return (
    <SidebarContainer collapsed={collapsed}>
      <SidebarHeader>
        <LogoContainer collapsed={collapsed}>
          <LogoIcon />
          {!collapsed && <LogoText>نظام الصيانة</LogoText>}
        </LogoContainer>
        <CollapseButton onClick={() => setCollapsed(!collapsed)}>
          {collapsed ? <FaBars /> : <FaTimes />}
        </CollapseButton>
      </SidebarHeader>

      <MenuList>
        {menuItems.map((item) => {
          const IconComponent = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <MenuItem key={item.path}>
              <MenuLink to={item.path} active={isActive}>
                <MenuIcon collapsed={collapsed}>
                  <IconComponent />
                </MenuIcon>
                {!collapsed && <MenuText collapsed={collapsed}>{item.text}</MenuText>}
              </MenuLink>
            </MenuItem>
          );
        })}
      </MenuList>

      <LogoutButton onClick={handleLogout}>
        <MenuIcon collapsed={collapsed}>
          <FaSignOutAlt />
        </MenuIcon>
        {!collapsed && <MenuText collapsed={collapsed}>تسجيل الخروج</MenuText>}
      </LogoutButton>
    </SidebarContainer>
  );
}

export default Sidebar;
