<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="نظام إدارة صيانة محل الموبايلات" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- خطوط عربية جميلة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <title>نظام إدارة صيانة الموبايلات</title>
    
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Cairo', sans-serif;
        direction: rtl;
        text-align: right;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        overflow-x: hidden;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* تحسين الخطوط العربية */
      .arabic-text {
        font-family: 'Cairo', sans-serif;
        font-weight: 400;
        line-height: 1.6;
      }
      
      /* تحسين الأزرار */
      .btn-3d {
        background: linear-gradient(145deg, #e6e6e6, #ffffff);
        box-shadow: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;
        border: none;
        border-radius: 15px;
        padding: 12px 24px;
        font-family: 'Cairo', sans-serif;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      
      .btn-3d:hover {
        transform: translateY(-2px);
        box-shadow: 25px 25px 70px #bebebe, -25px -25px 70px #ffffff;
      }
      
      .btn-3d:active {
        transform: translateY(0);
        box-shadow: inset 20px 20px 60px #bebebe, inset -20px -20px 60px #ffffff;
      }
      
      /* تحسين البطاقات */
      .card-3d {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        padding: 20px;
        margin: 10px;
        transition: all 0.3s ease;
      }
      
      .card-3d:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
      }
      
      /* تحسين حقول الإدخال */
      .input-3d {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 12px 16px;
        font-family: 'Cairo', sans-serif;
        font-size: 14px;
        transition: all 0.3s ease;
        width: 100%;
        margin: 8px 0;
      }
      
      .input-3d:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        background: rgba(255, 255, 255, 1);
      }
      
      /* تحسين الجداول */
      .table-3d {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
      }
      
      .table-3d th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        font-weight: 600;
        text-align: center;
      }
      
      .table-3d td {
        padding: 12px 15px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        text-align: center;
      }
      
      .table-3d tr:hover {
        background: rgba(102, 126, 234, 0.1);
      }
      
      /* تحسين شريط التمرير */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
      }
    </style>
  </head>
  <body>
    <noscript>يجب تفعيل JavaScript لتشغيل هذا التطبيق.</noscript>
    <div id="root"></div>
  </body>
</html>
