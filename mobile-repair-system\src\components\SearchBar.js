import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { FaSearch, FaTimes, Fa<PERSON>ser, Fa<PERSON>ist, FaCogs, FaBarcode } from 'react-icons/fa';

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
`;

const SearchInputContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md} 50px ${props => props.theme.spacing.md} ${props => props.theme.spacing.md};
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: ${props => props.theme.borderRadius.xlarge};
  background: rgba(255, 255, 255, 0.95);
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fonts.sizes.medium};
  transition: all 0.3s ease;
  text-align: right;
  box-shadow: ${props => props.theme.shadows.medium};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 25px rgba(102, 126, 234, 0.4);
    background: rgba(255, 255, 255, 1);
  }

  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
    text-align: right;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.primary};
  font-size: 18px;
  z-index: 2;
`;

const ClearButton = styled.button`
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 16px;
  cursor: pointer;
  z-index: 2;
  transition: color 0.3s ease;
  opacity: ${props => props.show ? '1' : '0'};
  pointer-events: ${props => props.show ? 'auto' : 'none'};

  &:hover {
    color: ${props => props.theme.colors.danger};
  }
`;

const SearchResults = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: ${props => props.theme.colors.cardBackground};
  border-radius: ${props => props.theme.borderRadius.medium};
  box-shadow: ${props => props.theme.shadows.large};
  border: 1px solid rgba(255, 255, 255, 0.18);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: ${props => props.theme.spacing.sm};
  opacity: ${props => props.show ? '1' : '0'};
  transform: translateY(${props => props.show ? '0' : '-10px'});
  transition: all 0.3s ease;
  pointer-events: ${props => props.show ? 'auto' : 'none'};
`;

const SearchCategory = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  &:last-child {
    border-bottom: none;
  }
`;

const CategoryTitle = styled.h4`
  font-size: ${props => props.theme.fonts.sizes.medium};
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.sm};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const SearchResultItem = styled.div`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  cursor: pointer;
  border-radius: ${props => props.theme.borderRadius.small};
  transition: all 0.3s ease;
  margin: ${props => props.theme.spacing.xs} 0;

  &:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(-5px);
  }
`;

const ResultTitle = styled.div`
  font-weight: 500;
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const ResultSubtitle = styled.div`
  font-size: ${props => props.theme.fonts.sizes.small};
  color: ${props => props.theme.colors.textSecondary};
`;

const NoResults = styled.div`
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
  color: ${props => props.theme.colors.textSecondary};
  font-style: italic;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${props => props.theme.spacing.xl};
`;

const Spinner = styled.div`
  width: 30px;
  height: 30px;
  border: 3px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  border-top-color: ${props => props.theme.colors.primary};
  animation: spin 1s ease-in-out infinite;

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

function SearchBar({ onResultClick }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showResults, setShowResults] = useState(false);
  const [loading, setLoading] = useState(false);
  const searchRef = useRef(null);

  // محاكاة البحث في قاعدة البيانات
  const performSearch = async (term) => {
    if (!term.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    setLoading(true);
    
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 300));

    // محاكاة نتائج البحث
    const mockResults = {
      customers: [
        { id: 1, name: 'أحمد محمد علي', phone: '07901234567', type: 'customer' },
        { id: 2, name: 'فاطمة حسن', phone: '07801234567', type: 'customer' }
      ],
      orders: [
        { id: 1, orderNumber: '20241220001', customerName: 'أحمد محمد', problem: 'شاشة مكسورة', type: 'order' },
        { id: 2, orderNumber: '20241220002', customerName: 'فاطمة حسن', problem: 'بطارية تالفة', type: 'order' }
      ],
      spareParts: [
        { id: 1, name: 'شاشة iPhone 13', barcode: '123456789', price: 150000, type: 'spare_part' },
        { id: 2, name: 'بطارية Samsung Galaxy', barcode: '987654321', price: 75000, type: 'spare_part' }
      ]
    };

    // فلترة النتائج بناءً على مصطلح البحث
    const filteredResults = {
      customers: mockResults.customers.filter(customer => 
        customer.name.includes(term) || customer.phone.includes(term)
      ),
      orders: mockResults.orders.filter(order => 
        order.orderNumber.includes(term) || 
        order.customerName.includes(term) || 
        order.problem.includes(term)
      ),
      spareParts: mockResults.spareParts.filter(part => 
        part.name.includes(term) || 
        part.barcode.includes(term)
      )
    };

    setSearchResults(filteredResults);
    setShowResults(true);
    setLoading(false);
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleClear = () => {
    setSearchTerm('');
    setSearchResults([]);
    setShowResults(false);
  };

  const handleResultClick = (result) => {
    setShowResults(false);
    if (onResultClick) {
      onResultClick(result);
    }
  };

  const hasResults = searchResults.customers?.length > 0 || 
                    searchResults.orders?.length > 0 || 
                    searchResults.spareParts?.length > 0;

  return (
    <SearchContainer ref={searchRef}>
      <SearchInputContainer>
        <SearchIcon>
          <FaSearch />
        </SearchIcon>
        <SearchInput
          type="text"
          placeholder="البحث في النظام... (العملاء، الطلبات، قطع الغيار، الباركود)"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => searchTerm && setShowResults(true)}
        />
        <ClearButton 
          show={searchTerm.length > 0}
          onClick={handleClear}
          type="button"
        >
          <FaTimes />
        </ClearButton>
      </SearchInputContainer>

      <SearchResults show={showResults && (loading || hasResults || searchTerm.length > 0)}>
        {loading ? (
          <LoadingSpinner>
            <Spinner />
          </LoadingSpinner>
        ) : hasResults ? (
          <>
            {searchResults.customers?.length > 0 && (
              <SearchCategory>
                <CategoryTitle>
                  <FaUser />
                  العملاء
                </CategoryTitle>
                {searchResults.customers.map(customer => (
                  <SearchResultItem 
                    key={customer.id} 
                    onClick={() => handleResultClick(customer)}
                  >
                    <ResultTitle>{customer.name}</ResultTitle>
                    <ResultSubtitle>{customer.phone}</ResultSubtitle>
                  </SearchResultItem>
                ))}
              </SearchCategory>
            )}

            {searchResults.orders?.length > 0 && (
              <SearchCategory>
                <CategoryTitle>
                  <FaList />
                  الطلبات
                </CategoryTitle>
                {searchResults.orders.map(order => (
                  <SearchResultItem 
                    key={order.id} 
                    onClick={() => handleResultClick(order)}
                  >
                    <ResultTitle>طلب رقم: {order.orderNumber}</ResultTitle>
                    <ResultSubtitle>{order.customerName} - {order.problem}</ResultSubtitle>
                  </SearchResultItem>
                ))}
              </SearchCategory>
            )}

            {searchResults.spareParts?.length > 0 && (
              <SearchCategory>
                <CategoryTitle>
                  <FaCogs />
                  قطع الغيار
                </CategoryTitle>
                {searchResults.spareParts.map(part => (
                  <SearchResultItem 
                    key={part.id} 
                    onClick={() => handleResultClick(part)}
                  >
                    <ResultTitle>{part.name}</ResultTitle>
                    <ResultSubtitle>
                      <FaBarcode style={{ marginLeft: '5px' }} />
                      {part.barcode} - {part.price.toLocaleString()} دينار
                    </ResultSubtitle>
                  </SearchResultItem>
                ))}
              </SearchCategory>
            )}
          </>
        ) : searchTerm.length > 0 ? (
          <NoResults>
            لا توجد نتائج للبحث "{searchTerm}"
          </NoResults>
        ) : null}
      </SearchResults>
    </SearchContainer>
  );
}

export default SearchBar;
