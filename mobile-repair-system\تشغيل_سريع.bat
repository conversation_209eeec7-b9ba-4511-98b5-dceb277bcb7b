@echo off
chcp 65001 >nul
title تشغيل سريع - نظام إدارة صيانة الموبايلات
color 0B

echo.
echo ========================================
echo     تشغيل سريع للنظام
echo ========================================
echo.

echo جاري فحص المتطلبات...

:: فحص وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    echo ثم إعادة تشغيل هذا الملف
    echo.
    pause
    exit
)

echo ✅ Node.js متوفر

:: فحص وجود المكتبات
if not exist "node_modules" (
    echo.
    echo جاري تثبيت المكتبات للمرة الأولى...
    echo هذا قد يستغرق بضع دقائق...
    echo.
    
    call npm install react react-dom react-router-dom styled-components react-icons react-scripts
    
    if errorlevel 1 (
        echo.
        echo ❌ فشل في تثبيت المكتبات!
        echo تأكد من اتصال الإنترنت وإعادة المحاولة
        pause
        exit
    )
    
    echo ✅ تم تثبيت المكتبات بنجاح!
)

echo.
echo ========================================
echo        بدء تشغيل النظام
echo ========================================
echo.

echo 🚀 جاري تشغيل النظام...
echo.
echo بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: abd
echo 🔑 كلمة المرور: ZAin1998
echo.
echo سيتم فتح النظام في المتصفح خلال 5 ثوانٍ...
echo.

:: انتظار 5 ثوانٍ ثم فتح المتصفح
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo 🌐 تم فتح المتصفح...
echo.
echo ⚠️  لا تغلق هذه النافذة أثناء استخدام النظام
echo.

:: تشغيل النظام
call npm start

echo.
echo تم إيقاف النظام
pause
