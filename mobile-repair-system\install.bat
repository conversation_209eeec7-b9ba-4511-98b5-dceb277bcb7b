@echo off
chcp 65001 >nul
title تثبيت نظام إدارة صيانة الموبايلات
color 0B

echo.
echo ========================================
echo     تثبيت نظام إدارة صيانة الموبايلات
echo ========================================
echo.

echo جاري فحص متطلبات النظام...
echo.

:: فحص وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت على النظام
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit
) else (
    echo ✅ Node.js مثبت
)

:: فحص وجود npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر
    pause
    exit
) else (
    echo ✅ npm متوفر
)

echo.
echo جاري تثبيت المكتبات الأساسية...
echo.

:: تثبيت المكتبات الأساسية
call npm install react react-dom react-router-dom styled-components react-icons

echo.
echo جاري تثبيت React Scripts...
echo.

call npm install react-scripts

echo.
echo جاري تثبيت Electron...
echo.

call npm install electron --save-dev

echo.
echo جاري تثبيت المكتبات الإضافية...
echo.

call npm install better-sqlite3 jspdf html2canvas exceljs qrcode date-fns react-select react-datepicker

echo.
echo ========================================
echo        تم التثبيت بنجاح! ✅
echo ========================================
echo.

echo لتشغيل النظام:
echo 1. انقر نقراً مزدوجاً على ملف start.bat
echo 2. أو اكتب في Terminal: npm start
echo.

echo بيانات تسجيل الدخول:
echo اسم المستخدم: abd
echo كلمة المرور: ZAin1998
echo.

echo إنشاء اختصار على سطح المكتب...

:: إنشاء اختصار على سطح المكتب
set "desktop=%USERPROFILE%\Desktop"
set "shortcut=%desktop%\نظام إدارة صيانة الموبايلات.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut%'); $Shortcut.TargetPath = '%CD%\start.bat'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.IconLocation = '%CD%\icon.ico'; $Shortcut.Description = 'نظام إدارة صيانة الموبايلات'; $Shortcut.Save()"

echo.
echo ✅ تم إنشاء اختصار على سطح المكتب
echo.

pause
