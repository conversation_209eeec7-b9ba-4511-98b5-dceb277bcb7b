@echo off
title Mobile Repair System - Quick Start
color 0B

echo.
echo ========================================
echo     Mobile Repair System - Quick Start
echo ========================================
echo.

echo Checking requirements...

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ERROR: Node.js is not installed!
    echo Please download and install Node.js from: https://nodejs.org
    echo Then restart this file
    echo.
    pause
    exit
)

echo OK: Node.js is available

:: Check if libraries are installed
if not exist "node_modules" (
    echo.
    echo Installing libraries for the first time...
    echo This may take a few minutes...
    echo.
    
    call npm install react react-dom react-router-dom styled-components react-icons react-scripts
    
    if errorlevel 1 (
        echo.
        echo ERROR: Failed to install libraries!
        echo Check internet connection and try again
        pause
        exit
    )
    
    echo OK: Libraries installed successfully!
)

echo.
echo ========================================
echo        Starting System
echo ========================================
echo.

echo Starting Mobile Repair System...
echo.
echo Login Credentials:
echo Username: abd
echo Password: ZAin1998
echo.
echo System will open in browser in 5 seconds...
echo.

:: Wait 5 seconds then open browser
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo Browser opened...
echo.
echo WARNING: Do not close this window while using the system
echo.

:: Start the system
call npm start

echo.
echo System stopped
pause
