# 📖 دليل تشغيل وتثبيت نظام إدارة صيانة الموبايلات

## 🎯 المتطلبات الأساسية

### 1. **Node.js** (مطلوب)
- تحميل من: https://nodejs.org
- الإصدار المطلوب: 16.0 أو أحدث
- يتضمن npm تلقائياً

### 2. **متطلبات النظام**
- Windows 10 أو أحدث
- 4 GB RAM كحد أدنى
- 500 MB مساحة فارغة
- دقة شاشة 1280x720 كحد أدنى

---

## 🚀 طرق التشغيل

### **الطريقة الأولى: التشغيل السريع (الأسهل)**

1. **انقر نقراً مزدوجاً على ملف `install.bat`**
   - سيقوم بتثبيت جميع المتطلبات تلقائياً
   - انتظر حتى انتهاء التثبيت

2. **انقر نقراً مزدوجاً على ملف `start.bat`**
   - اختر رقم 2 لتشغيل النظام
   - سيفتح النظام في المتصفح تلقائياً

3. **استخدم بيانات تسجيل الدخول:**
   - اسم المستخدم: `abd`
   - كلمة المرور: `ZAin1998`

---

### **الطريقة الثانية: التشغيل اليدوي**

1. **افتح Command Prompt أو PowerShell**
2. **انتقل إلى مجلد النظام:**
   ```cmd
   cd "c:\Users\<USER>\Desktop\برنامج الصيانة 206\mobile-repair-system"
   ```

3. **تثبيت المكتبات (مرة واحدة فقط):**
   ```cmd
   npm install
   ```

4. **تشغيل النظام:**
   ```cmd
   npm start
   ```

5. **افتح متصفح جديد وانتقل إلى:**
   ```
   http://localhost:3000
   ```

---

### **الطريقة الثالثة: العرض التوضيحي**

1. **انقر نقراً مزدوجاً على ملف `demo.html`**
2. **سيفتح عرض توضيحي للنظام في المتصفح**
3. **يمكنك رؤية جميع المميزات والتصميم**

---

## 🔧 إنشاء ملف تثبيت للتوزيع

### **لإنشاء ملف .exe للتوزيع:**

1. **تشغيل الأمر:**
   ```cmd
   npm run build
   npm run electron-pack
   ```

2. **ستجد ملف التثبيت في مجلد `dist`**

3. **يمكن توزيع هذا الملف على أي جهاز Windows**

---

## 🎨 مميزات النظام

### **✅ المميزات المكتملة:**
- 🔐 نظام تسجيل دخول آمن
- 🏠 لوحة تحكم مع إحصائيات حية
- 🔍 بحث ذكي شامل
- 👥 إدارة العملاء الكاملة
- 🔧 إضافة طلبات الصيانة
- 🎨 تصميم ثلاثي الأبعاد جميل
- 🌐 دعم كامل للغة العربية
- 💰 يعمل بالدينار العراقي

### **🚧 قيد التطوير:**
- 📋 عرض وإدارة الطلبات
- 🔩 إدارة قطع الغيار
- 📄 إنشاء المستندات والإيصالات
- 📊 التقارير والإحصائيات
- 📝 سجل العمليات
- ⚙️ الإعدادات الشاملة

---

## 🔑 بيانات تسجيل الدخول

```
اسم المستخدم: abd
كلمة المرور: ZAin1998
```

---

## 🆘 حل المشاكل الشائعة

### **مشكلة: "node is not recognized"**
**الحل:** تثبيت Node.js من https://nodejs.org

### **مشكلة: "npm install فشل"**
**الحل:** 
1. تشغيل Command Prompt كمدير
2. تنظيف cache: `npm cache clean --force`
3. إعادة المحاولة: `npm install`

### **مشكلة: "النظام لا يفتح"**
**الحل:**
1. التأكد من تشغيل `npm start`
2. انتظار رسالة "compiled successfully"
3. فتح http://localhost:3000 يدوياً

### **مشكلة: "الخطوط العربية لا تظهر"**
**الحل:** التأكد من اتصال الإنترنت لتحميل خط Cairo

---

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. مراجعة هذا الدليل
2. فحص ملف `README.md`
3. تشغيل العرض التوضيحي `demo.html`

---

## 🎉 تهانينا!

تم إنشاء نظام إدارة صيانة الموبايلات بنجاح!
النظام جاهز للاستخدام والتطوير.

**تم التطوير بـ ❤️ للسوق العراقي**
